use indicatif::MultiProgress;
use std::fs;
use std::path::{Path, PathBuf};
use std::time::{SystemTime, UNIX_EPOCH, Duration};

use crate::{print_debug, print_info, print_success, print_warn};

// 全局目录配置
static mut MUSIC_DIR: Option<PathBuf> = None;
static mut TEMP_DIR: Option<PathBuf> = None;
static mut PROGRESS_MANAGER: Option<MultiProgress> = None;
static mut CURRENT_BROWSER_DATA_DIR: Option<PathBuf> = None; // 当前浏览器数据目录

// ==================== 信号处理和清理 ====================
pub fn setup_signal_handlers() {
    // 设置Ctrl+C处理
    if let Err(e) = ctrlc::set_handler(move || {
        print_warn!("\n⚠️  检测到Ctrl+C，正在安全退出...");
        cleanup_current_browser_data();
        std::process::exit(0);
    }) {
        print_warn!("设置Ctrl+C处理器失败: {}", e);
    }
}

pub fn cleanup_and_exit() -> Result<(), Box<dyn std::error::Error>> {
    crate::file_handler::cleanup_temp_directory();
    cleanup_current_browser_data();
    print_info!("程序已安全退出");
    Ok(())
}

pub fn cleanup_current_browser_data() {
    unsafe {
        if let Some(browser_data_dir) = CURRENT_BROWSER_DATA_DIR.as_ref() {
            if browser_data_dir.exists() {
                match fs::remove_dir_all(browser_data_dir) {
                    Ok(_) => {
                        print_debug!(
                            "✓ 当前browser_data目录已清理: {}",
                            browser_data_dir.display()
                        );
                    }
                    Err(e) => {
                        print_warn!("⚠️  清理当前browser_data目录失败: {}", e);
                    }
                }
            }
        }
    }
}

// ==================== 初始化和环境设置 ====================
pub fn initialize_environment() -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        MUSIC_DIR = Some(get_download_directory()?);
        TEMP_DIR = Some(get_music_directory().join(".temp_downloads"));
        PROGRESS_MANAGER = Some(MultiProgress::new());

        // 生成唯一的browser_data目录名（在下载目录下）
        CURRENT_BROWSER_DATA_DIR = Some(generate_unique_browser_data_dir_in_downloads());
    }

    // 启动时清理下载目录中遗留的browser_data目录
    cleanup_legacy_browser_data_dirs_in_downloads()?;

    // 确保目录存在
    fs::create_dir_all(get_temp_directory())?;

    // 创建当前的browser_data目录并设置隐藏属性
    create_and_hide_browser_data_dir()?;

    print_debug!("环境初始化完成");
    print_debug!("音乐目录: {}", get_music_directory().display());
    print_debug!("临时目录: {}", get_temp_directory().display());
    print_debug!(
        "浏览器数据目录: {}",
        get_current_browser_data_dir().display()
    );

    Ok(())
}

/// 在下载目录中生成唯一的browser_data目录名
fn generate_unique_browser_data_dir_in_downloads() -> PathBuf {
    let music_dir = unsafe { MUSIC_DIR.as_ref().unwrap() };
    let process_id = std::process::id();
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    // 格式：.browser_data_进程ID_时间戳（隐藏目录）
    let dir_name = format!(".browser_data_{}_{}", process_id, timestamp);
    music_dir.join(dir_name)
}

/// 创建browser_data目录并设置隐藏属性
fn create_and_hide_browser_data_dir() -> Result<(), Box<dyn std::error::Error>> {
    let browser_data_dir = get_current_browser_data_dir();

    // 创建目录
    fs::create_dir_all(browser_data_dir)?;

    // Unix系统通过.前缀自动隐藏，无需额外操作
    Ok(())
}

/// 清理下载目录中遗留的browser_data目录
fn cleanup_legacy_browser_data_dirs_in_downloads() -> Result<(), Box<dyn std::error::Error>> {
    let music_dir = get_music_directory();
    print_info!("🧹 检查下载目录中遗留的browser_data目录...");

    let mut cleaned_count = 0;
    let mut total_size_freed = 0u64;

    // 扫描下载目录中的browser_data相关目录
    if let Ok(entries) = fs::read_dir(music_dir) {
        for entry in entries.flatten() {
            let path = entry.path();

            if path.is_dir() {
                let dir_name = path
                    .file_name()
                    .and_then(|name| name.to_str())
                    .unwrap_or("");

                // 检查是否是browser_data相关目录
                if is_browser_data_directory(dir_name) {
                    // 检查目录是否正在被使用
                    if !is_directory_in_use(&path) {
                        // 计算目录大小
                        if let Ok(size) = calculate_directory_size(&path) {
                            total_size_freed += size;
                        }

                        // 删除目录
                        match fs::remove_dir_all(&path) {
                            Ok(_) => {
                                print_debug!("✓ 已清理遗留目录: {}", dir_name);
                                cleaned_count += 1;
                            }
                            Err(e) => {
                                print_warn!("⚠️  无法清理目录 {}: {}", dir_name, e);
                            }
                        }
                    } else {
                        print_debug!("跳过正在使用的目录: {}", dir_name);
                    }
                }
            }
        }
    }

    if cleaned_count > 0 {
        print_success!(
            "✅ 已清理 {} 个遗留的browser_data目录，释放空间: {:.2} MB",
            cleaned_count,
            total_size_freed as f64 / 1024.0 / 1024.0
        );
    } else {
        print_info!("✓ 下载目录中未发现需要清理的遗留目录");
    }

    Ok(())
}

/// 检查目录名是否是browser_data相关目录
fn is_browser_data_directory(dir_name: &str) -> bool {
    dir_name == ".browser_data" ||
    dir_name.starts_with(".browser_data_") ||
    // 兼容旧版本可能的命名方式
    dir_name == "browser_data" ||
    dir_name.starts_with("browser_data_") ||
    (dir_name.starts_with("browser_data") &&
     (dir_name.contains("_") || dir_name.chars().last().map_or(false, |c| c.is_ascii_digit())))
}

/// 检查目录是否正在被使用
fn is_directory_in_use(dir_path: &Path) -> bool {
    // 检查是否存在Chrome进程锁文件
    let lock_files = ["SingletonLock", "lockfile", ".lock", "Singleton"];

    for lock_file in &lock_files {
        if dir_path.join(lock_file).exists() {
            return true;
        }
    }

    // 检查目录是否最近被修改（5分钟内）
    if let Ok(metadata) = fs::metadata(dir_path) {
        if let Ok(modified) = metadata.modified() {
            if let Ok(elapsed) = modified.elapsed() {
                // 如果目录在5分钟内被修改，认为可能正在使用
                return elapsed < Duration::from_secs(300);
            }
        }
    }

    false
}

/// 计算目录大小
fn calculate_directory_size(dir: &Path) -> Result<u64, Box<dyn std::error::Error>> {
    let mut total_size = 0;

    fn visit_dir(dir: &Path, total_size: &mut u64) -> Result<(), Box<dyn std::error::Error>> {
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_dir() {
                visit_dir(&path, total_size)?;
            } else {
                *total_size += entry.metadata()?.len();
            }
        }
        Ok(())
    }

    visit_dir(dir, &mut total_size)?;
    Ok(total_size)
}

// ==================== 目录管理 ====================
pub fn get_download_directory() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let args: Vec<String> = std::env::args().collect();
    if args.len() > 1 {
        let specified_path = PathBuf::from(&args[1]);
        if !specified_path.exists() {
            fs::create_dir_all(&specified_path)?;
            print_success!("✓ 已创建指定目录: {}", specified_path.display());
        } else {
            print_info!("✓ 使用指定的下载目录: {}", specified_path.display());
        }
        return Ok(specified_path);
    }

    get_default_downloads_path()
}

fn get_default_downloads_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let downloads_path = match dirs::home_dir() {
        Some(home) => home.join("Downloads").join("nt_dl_downloads"),
        None => PathBuf::from("./nt_dl_downloads"),
    };
    fs::create_dir_all(&downloads_path)?;
    print_info!("✓ 使用默认下载目录: {}", downloads_path.display());
    print_info!("💡 提示: 下次可通过命令行参数指定自定义路径");
    Ok(downloads_path)
}

pub fn get_music_directory() -> &'static PathBuf {
    unsafe { MUSIC_DIR.as_ref().unwrap() }
}

pub fn get_temp_directory() -> &'static PathBuf {
    unsafe { TEMP_DIR.as_ref().unwrap() }
}

pub fn get_progress_manager() -> &'static MultiProgress {
    unsafe { PROGRESS_MANAGER.as_ref().unwrap() }
}

pub fn get_current_browser_data_dir() -> &'static PathBuf {
    unsafe { CURRENT_BROWSER_DATA_DIR.as_ref().unwrap() }
}