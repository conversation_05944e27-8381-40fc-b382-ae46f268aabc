use headless_chrome::protocol::cdp::Page;
use headless_chrome::protocol::cdp::Page::SetDownloadBehaviorBehaviorOption;
use headless_chrome::{<PERSON>rowser, LaunchOptionsBuilder, Tab};
use rand::{thread_rng, Rng};
use std::collections::HashMap;
use std::ffi::OsStr;
use std::fs;
use std::path::PathBuf;
use std::time::Duration;
use tokio::time::sleep;

use crate::config::{DOWNLOAD_TIMEOUT, MAX_RETRIES, TARGET_PAGE_URL, USER_AGENT};
use crate::env_setup::{get_current_browser_data_dir, get_temp_directory};
use crate::file_handler::{process_downloaded_file, is_download_complete};
use crate::ui::{create_lightweight_main_progress_bar, create_lightweight_task_indicator};
use crate::{print_debug, print_info, print_success};
use crate::utils::ProcessingError;

// ==================== 轻量级下载流程控制 ====================
pub async fn execute_lightweight_download_workflow(
    urls: &[String],
    quality: &str,
) -> HashMap<String, String> {
    let mut failed_tasks: HashMap<String, String> = HashMap::new();

    // 创建轻量级主进度条
    let main_progress = create_lightweight_main_progress_bar(urls.len());

    // 主重试循环
    for attempt in 0..=MAX_RETRIES {
        let urls_remaining = if attempt == 0 {
            urls.to_vec()
        } else {
            failed_tasks.keys().cloned().collect()
        };

        if urls_remaining.is_empty() {
            break;
        }

        if attempt > 0 {
            main_progress.println(format!(
                "\n{} 开始重试失败的任务 (第 {}/{} 次重试) {}",
                "=".repeat(10),
                attempt,
                MAX_RETRIES,
                "=".repeat(10)
            ));
            sleep(Duration::from_millis(3000)).await;
        }

        main_progress.println(format!(
            "📋 开始处理 {} 个任务，串行模式确保稳定性",
            urls_remaining.len()
        ));

        // 轻量级处理 - 减少进度更新频率
        let mut next_round_failures = HashMap::new();
        for (index, url) in urls_remaining.iter().enumerate() {
            // 只在关键节点更新进度信息
            if index % 5 == 0 || index == urls_remaining.len() - 1 {
                main_progress.set_message(format!(
                    "正在处理第 {}/{} 个任务",
                    index + 1,
                    urls_remaining.len()
                ));
            }

            match process_single_url_lightweight(url, quality, index + 1, urls_remaining.len())
                .await
            {
                Ok(_) => {
                    // 只在成功时显示消息，减少输出
                    main_progress.println(format!(
                        "✅ 成功: 第{}/{}个任务",
                        index + 1,
                        urls_remaining.len()
                    ));
                    failed_tasks.remove(url);
                }
                Err(e) => {
                    main_progress.println(format!(
                        "❌ 失败: 第{}/{}个任务",
                        index + 1,
                        urls_remaining.len()
                    ));
                    main_progress.println(format!("   错误: {}", e.message));
                    next_round_failures.insert(url.clone(), e.message);
                }
            }

            main_progress.inc(1);

            // 适度延迟，避免过于频繁
            let delay = thread_rng().gen_range(1500..3000);
            sleep(Duration::from_millis(delay)).await;
        }

        failed_tasks.extend(next_round_failures);
    }

    main_progress.finish_with_message("所有任务处理完成");
    failed_tasks
}

// ==================== 轻量级单任务处理 ====================
async fn process_single_url_lightweight(
    url: &str,
    quality_text: &str,
    current: usize,
    total: usize,
) -> Result<(), ProcessingError> {
    // 显示简洁的任务信息
    println!("{}", create_lightweight_task_indicator(url, current, total));

    // 环境准备
    let temp_dir = get_temp_directory();
    fs::create_dir_all(temp_dir)
        .map_err(|e| ProcessingError::new(&format!("创建临时目录失败: {}", e)))?;

    // 启动浏览器 - 使用新的browser_data位置
    let browser =
        launch_browser().map_err(|e| ProcessingError::new(&format!("启动浏览器失败: {}", e)))?;

    let tab = browser
        .new_tab()
        .map_err(|e| ProcessingError::new(&format!("创建标签页失败: {}", e)))?;

    // 配置下载环境
    setup_download_behavior(&tab)
        .map_err(|e| ProcessingError::new(&format!("设置下载行为失败: {}", e)))?;

    // 导航到目标页面
    tab.set_user_agent(USER_AGENT, None, None)
        .map_err(|e| ProcessingError::new(&format!("设置User-Agent失败: {}", e)))?;

    tab.navigate_to(TARGET_PAGE_URL)
        .map_err(|e| ProcessingError::new(&format!("导航到目标页面失败: {}", e)))?;

    // 初始等待
    sleep(Duration::from_millis(3000)).await;

    // 等待页面准备就绪 - 使用轻量级检测
    wait_for_page_ready_lightweight(&tab)
        .await
        .map_err(|e| ProcessingError::new(&format!("页面加载失败: {}", e)))?;

    // 填写音乐链接
    fill_music_url(&tab, url)
        .await
        .map_err(|e| ProcessingError::new(&format!("填写URL失败: {}", e)))?;

    // 选择音质
    select_quality(&tab, quality_text)
        .await
        .map_err(|e| ProcessingError::new(&format!("选择音质失败: {}", e)))?;

    // 选择下载类型
    select_download_type(&tab)
        .await
        .map_err(|e| ProcessingError::new(&format!("选择下载类型失败: {}", e)))?;

    // 触发解析
    click_parse_button(&tab)
        .await
        .map_err(|e| ProcessingError::new(&format!("点击解析按钮失败: {}", e)))?;

    // 确认解析
    click_confirm_button(&tab)
        .await
        .map_err(|e| ProcessingError::new(&format!("点击确认按钮失败: {}", e)))?;

    // 等待下载按钮
    wait_for_download_button(&tab)
        .await
        .map_err(|e| ProcessingError::new(&format!("等待下载按钮失败: {}", e)))?;

    // 执行下载
    let download_path = trigger_download(&tab, url)
        .await
        .map_err(|e| ProcessingError::new(&format!("下载失败: {}", e)))?;

    // 处理文件
    process_downloaded_file(&download_path)
        .await
        .map_err(|e| ProcessingError::new(&format!("处理下载文件失败: {}", e)))?;

    // 清理
    tab.close(true).ok();

    Ok(())
}

// ==================== 轻量级页面检测 ====================
async fn wait_for_page_ready_lightweight(tab: &Tab) -> Result<(), Box<dyn std::error::Error>> {
    // 简化的检测脚本，减少监控开销
    let simple_check_script = r#"
        new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject('页面加载超时'), 20000);

            const quickCheck = () => {
                if (document.readyState === 'complete') {
                    const headers = document.querySelectorAll("h5");
                    const targetHeader = Array.from(headers).find(h =>
                        h.textContent.includes('网易云无损解析')
                    );

                    if (targetHeader && document.querySelector("input[type='text']")) {
                        clearTimeout(timeout);
                        resolve('页面就绪');
                        return;
                    }
                }
                setTimeout(quickCheck, 1000); // 降低检查频率
            };

            quickCheck();
        })
    "#;

    tab.evaluate(simple_check_script, false)?;
    Ok(())
}

// ==================== 浏览器操作 (使用新的browser_data位置) ====================
fn launch_browser() -> Result<Browser, Box<dyn std::error::Error>> {
    let temp_path = get_temp_directory();
    let download_path = temp_path.canonicalize()?.to_string_lossy().to_string();
    let download_dir_arg = format!("--download-directory={}", download_path);

    // 使用下载目录中的隐藏browser_data目录
    let browser_data_dir = get_current_browser_data_dir();

    // 使用验证过的稳定参数
    let args: Vec<&OsStr> = vec![
        OsStr::new("--no-sandbox"),
        OsStr::new("--disable-setuid-sandbox"),
        OsStr::new("--disable-dev-shm-usage"),
        OsStr::new("--disable-gpu"),
        OsStr::new("--no-first-run"),
        OsStr::new("--disable-web-security"),
        OsStr::new("--enable-automation"),
        OsStr::new("--allow-running-insecure-content"),
        OsStr::new("--disable-background-timer-throttling"),
        OsStr::new("--disable-renderer-backgrounding"),
        OsStr::new("--disable-backgrounding-occluded-windows"),
        OsStr::new("--disable-blink-features=AutomationControlled"),
        OsStr::new("--disable-extensions"),
        OsStr::new("--no-default-browser-check"),
        OsStr::new("--disable-popup-blocking"),
        OsStr::new("--disable-translate"),
        OsStr::new("--disable-default-apps"),
        OsStr::new("--disable-sync"),
        OsStr::new("--no-pings"),
        OsStr::new("--allow-downloads"),
        OsStr::new("--disable-component-updates"),
        OsStr::new("--disable-background-networking"),
        OsStr::new("--disable-client-side-phishing-detection"),
        OsStr::new(&download_dir_arg),
    ];

    let launch_options = LaunchOptionsBuilder::default()
        .headless(true)
        .sandbox(false)
        .args(args)
        .user_data_dir(Some(browser_data_dir.clone())) // 使用新的隐藏目录
        .build()
        .map_err(|e| format!("构建浏览器启动选项失败: {}", e))?;

    let browser = Browser::new(launch_options).map_err(|e| format!("启动浏览器失败: {}", e))?;

    print_debug!("浏览器启动成功，数据目录: {}", browser_data_dir.display());
    Ok(browser)
}

fn setup_download_behavior(tab: &Tab) -> Result<(), Box<dyn std::error::Error>> {
    let temp_path = get_temp_directory();
    let download_path = temp_path.canonicalize()?.to_string_lossy().to_string();

    tab.call_method(Page::SetDownloadBehavior {
        behavior: SetDownloadBehaviorBehaviorOption::Allow,
        download_path: Some(download_path),
    })?;

    Ok(())
}

async fn fill_music_url(tab: &Tab, url: &str) -> Result<(), Box<dyn std::error::Error>> {
    let script = format!(
        r#"
        document.querySelector("[placeholder*='请输入网易云音乐链接']").value = '{}';
        document.querySelector("[placeholder*='请输入网易云音乐链接']").dispatchEvent(new Event('input', {{ bubbles: true }}));
        "#,
        url
    );
    tab.evaluate(&script, false)?;
    sleep(Duration::from_millis(1000)).await;
    Ok(())
}

async fn select_quality(tab: &Tab, quality_text: &str) -> Result<(), Box<dyn std::error::Error>> {
    tab.evaluate(
        "document.querySelector('.ep-select__wrapper').click();",
        false,
    )?;
    sleep(Duration::from_millis(1000)).await;

    let select_quality_script = format!(
        r#"
        const options = Array.from(document.querySelectorAll('li'));
        const targetOption = options.find(li => li.textContent.includes('{}'));
        if (targetOption) {{
            targetOption.click();
        }} else {{
            const fallbackOption = options.find(li => li.textContent.includes('高解析度无损(VIP)'));
            if (fallbackOption) {{
                fallbackOption.click();
            }}
        }}
        "#,
        quality_text
    );
    tab.evaluate(&select_quality_script, false)?;
    sleep(Duration::from_millis(1000)).await;
    Ok(())
}

async fn select_download_type(tab: &Tab) -> Result<(), Box<dyn std::error::Error>> {
    sleep(Duration::from_millis(1000)).await;
    tab.evaluate(
        "document.querySelectorAll('.ep-select__wrapper')[1].click();",
        false,
    )?;
    sleep(Duration::from_millis(1000)).await;
    tab.evaluate("Array.from(document.querySelectorAll('li')).find(li => li.textContent.includes('单曲')).click();", false)?;
    sleep(Duration::from_millis(1000)).await;
    Ok(())
}

async fn click_parse_button(tab: &Tab) -> Result<(), Box<dyn std::error::Error>> {
    sleep(Duration::from_millis(1000)).await;
    tab.evaluate("Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('立即解析')).click();", false)?;
    sleep(Duration::from_millis(2000)).await;
    Ok(())
}

async fn click_confirm_button(tab: &Tab) -> Result<(), Box<dyn std::error::Error>> {
    sleep(Duration::from_millis(2000)).await;
    let script = r#"
        const confirmButton = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('确认'));
        if (confirmButton) confirmButton.click();
    "#;
    tab.evaluate(script, false)?;
    sleep(Duration::from_millis(2000)).await;
    Ok(())
}

async fn wait_for_download_button(tab: &Tab) -> Result<(), Box<dyn std::error::Error>> {
    let wait_script = r#"
        new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject('等待下载按钮超时'), 60000);
            const checkDownloadButton = () => {
                const downloadButton = Array.from(document.querySelectorAll('button'))
                    .find(btn => btn.textContent.includes('点击下载'));
                if (downloadButton) {
                    clearTimeout(timeout);
                    resolve('下载按钮已出现');
                } else {
                    setTimeout(checkDownloadButton, 2000);
                }
            };
            checkDownloadButton();
        })
    "#;
    tab.evaluate(wait_script, false)?;
    Ok(())
}

async fn trigger_download(tab: &Tab, url: &str) -> Result<PathBuf, Box<dyn std::error::Error>> {
    tab.evaluate("Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('点击下载')).click();", false)?;
    print_info!("⬇️  触发下载，开始文件监控...");

    let temp_dir = get_temp_directory();
    for i in 0..DOWNLOAD_TIMEOUT {
        sleep(Duration::from_secs(1)).await;

        if temp_dir.exists() {
            for entry in fs::read_dir(temp_dir)? {
                let entry = entry?;
                let path = entry.path();

                if path.is_file() {
                    let file_name = path.file_name().unwrap().to_string_lossy();
                    let file_size = path.metadata()?.len();

                    if (file_name.ends_with(".zip") || file_name.ends_with(".rar"))
                        && file_size > 1000
                    {
                        if is_download_complete(&path)? {
                            print_success!(
                                "✅ 文件下载完成: {} ({} KB)",
                                file_name,
                                file_size / 1024
                            );
                            return Ok(path);
                        }
                    }
                }
            }
        }

        // 只在30秒间隔显示等待信息，减少输出
        if i % 30 == 0 && i > 0 {
            print_info!("⏳ 等待下载... ({}/{}秒)", i, DOWNLOAD_TIMEOUT);
        }
    }

    Err(format!("下载超时，未找到下载文件: {}", url).into())
}