use std::process::Command;

// 颜色输出宏
#[macro_export]
macro_rules! print_success {
    ($($arg:tt)*) => {
        println!("\x1b[92m{}\x1b[0m", format!($($arg)*));
    };
}

#[macro_export]
macro_rules! print_info {
    ($($arg:tt)*) => {
        println!("\x1b[94m{}\x1b[0m", format!($($arg)*));
    };
}

#[macro_export]
macro_rules! print_warn {
    ($($arg:tt)*) => {
        println!("\x1b[93m{}\x1b[0m", format!($($arg)*));
    };
}

#[macro_export]
macro_rules! print_error {
    ($($arg:tt)*) => {
        println!("\x1b[91m{}\x1b[0m", format!($($arg)*));
    };
}

#[macro_export]
macro_rules! print_debug {
    ($($arg:tt)*) => {
        if cfg!(debug_assertions) {
            println!("\x1b[90m[DEBUG] {}\x1b[0m", format!($($arg)*));
        }
    };
}

// 自定义错误类型
#[derive(Debug, Clone)]
pub struct ProcessingError {
    pub message: String,
}

impl std::fmt::Display for ProcessingError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{}", self.message)
    }
}

impl std::error::Error for ProcessingError {}

impl ProcessingError {
    pub fn new(message: &str) -> Self {
        Self {
            message: message.to_string(),
        }
    }
}

// ==================== 依赖检查 ====================
pub fn check_ffmpeg() -> bool {
    Command::new("ffmpeg").arg("-version").output().is_ok()
}
