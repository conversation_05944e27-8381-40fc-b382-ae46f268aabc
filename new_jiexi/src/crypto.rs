use aes::cipher::{block_padding::Pkcs7, BlockEncryptMut, KeyIvInit};
use base64::{engine::general_purpose, Engine as _};
use cbc::Encryptor;
use num_bigint::BigUint;
use num_traits::Num;
use rand::{thread_rng, Rng};
use serde_json::Value;
use std::collections::HashMap;

use crate::config::{MODULUS, NONCE, PUBKEY};

// AES加密器类型别名
type Aes128CbcEnc = Encryptor<aes::Aes128>;

// ==================== 网易云API加密函数 ====================
fn generate_random_key(length: usize) -> String {
    let seed = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let mut rng = thread_rng();
    (0..length)
        .map(|_| seed.chars().nth(rng.gen_range(0..seed.len())).unwrap())
        .collect()
}

fn aes_encrypt(data: &str, key: &str) -> Result<String, Box<dyn std::error::Error>> {
    let iv = b"0102030405060708";
    let key_bytes = key.as_bytes();
    let cipher = Aes128CbcEnc::new(key_bytes.into(), iv.into());
    let ciphertext = cipher.encrypt_padded_vec_mut::<Pkcs7>(data.as_bytes());
    Ok(general_purpose::STANDARD.encode(&ciphertext))
}

fn rsa_encrypt(
    text: &str,
    pub_key: &str,
    modulus: &str,
) -> Result<String, Box<dyn std::error::Error>> {
    let text_bytes: Vec<u8> = text.bytes().rev().collect();
    let text_int = BigUint::from_bytes_be(&text_bytes);
    let pub_key_int = BigUint::from_str_radix(pub_key, 16)?;
    let modulus_int = BigUint::from_str_radix(modulus, 16)?;
    let encrypted_int = text_int.modpow(&pub_key_int, &modulus_int);
    let hex_result = format!("{:x}", encrypted_int);
    Ok(format!("{:0>256}", hex_result))
}

pub fn get_weapi_params(
    payload: &Value,
) -> Result<HashMap<String, String>, Box<dyn std::error::Error>> {
    let payload_str = payload.to_string();
    let secret_key = generate_random_key(16);
    let params = aes_encrypt(&payload_str, NONCE)?;
    let params = aes_encrypt(&params, &secret_key)?;
    let enc_sec_key = rsa_encrypt(&secret_key, PUBKEY, MODULUS)?;

    let mut result = HashMap::new();
    result.insert("params".to_string(), params);
    result.insert("encSecKey".to_string(), enc_sec_key);
    Ok(result)
}
