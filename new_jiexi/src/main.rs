use std::io::{self, Write};

mod config;
mod utils;
mod crypto;
mod api;
mod downloader;
mod file_handler;
mod ui;
mod env_setup;

use utils::check_ffmpeg;
use api::parse_and_route_url;
use downloader::execute_lightweight_download_workflow;
use env_setup::{cleanup_and_exit, initialize_environment, setup_signal_handlers};
use file_handler::handle_temp_directory_cleanup;
use ui::{display_final_results, display_program_header, select_audio_quality};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 设置Ctrl+C信号处理
    setup_signal_handlers();

    // 初始化环境（包含browser_data迁移和清理）
    initialize_environment()?;

    // 显示程序信息
    display_program_header();

    // 检查FFmpeg
    if !check_ffmpeg() {
        print_error!("❌ 严重错误: 未安装FFmpeg或未在系统PATH中找到");
        println!("💡 在macOS上，您可以通过Homebrew安装: brew install ffmpeg");
        return cleanup_and_exit();
    }

    // 获取用户输入URL
    print!("🔗 请输入网易云音乐URL: ");
    io::stdout().flush().unwrap();
    let mut raw_url = String::new();
    io::stdin().read_line(&mut raw_url)?;
    let raw_url = raw_url.trim();

    if raw_url.is_empty() {
        print_warn!("未输入URL，程序退出");
        return cleanup_and_exit();
    }

    // 解析URL获取歌曲列表
    let urls_to_process = tokio::runtime::Runtime::new().unwrap().block_on(async {
        parse_and_route_url(raw_url).await
    })?;

    if urls_to_process.is_empty() {
        print_error!("❌ 未能获取到有效的URL，程序退出");
        return cleanup_and_exit();
    }

    // 选择音质
    let selected_quality = select_audio_quality();

    // 执行轻量级下载流程
    let failed_tasks = tokio::runtime::Runtime::new().unwrap().block_on(async {
        execute_lightweight_download_workflow(&urls_to_process, &selected_quality).await
    });

    // 处理临时文件
    println!("\n{} 🧹 清理与恢复 {}", "=".repeat(25), "=".repeat(25));
    handle_temp_directory_cleanup()?;

    // 生成最终报告
    display_final_results(&urls_to_process, &failed_tasks);

    // 程序正常退出清理
    cleanup_and_exit()
}