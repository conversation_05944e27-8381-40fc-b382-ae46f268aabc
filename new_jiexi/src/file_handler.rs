use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::time::Duration;

use crate::env_setup::{get_music_directory, get_temp_directory};
use crate::{print_info, print_success, print_warn};

// ==================== 文件处理部分 ====================
pub async fn process_downloaded_file(download_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    let extract_dir = get_temp_directory().join(download_path.file_stem().unwrap());

    // 解压ZIP文件
    let file = fs::File::open(download_path)?;
    let mut archive = zip::ZipArchive::new(file)?;
    fs::create_dir_all(&extract_dir)?;

    for i in 0..archive.len() {
        let mut file = archive.by_index(i)?;
        let outpath = extract_dir.join(file.name());

        if file.name().ends_with('/') {
            fs::create_dir_all(&outpath)?;
        } else {
            if let Some(p) = outpath.parent() {
                if !p.exists() {
                    fs::create_dir_all(p)?;
                }
            }
            let mut outfile = fs::File::create(&outpath)?;
            std::io::copy(&mut file, &mut outfile)?;
        }
    }

    fs::remove_file(download_path)?;
    post_process_folder(&extract_dir)?;
    Ok(())
}

fn post_process_folder(extract_folder: &Path) -> Result<(), Box<dyn std::error::Error>> {
    let flac_files: Vec<_> = fs::read_dir(extract_folder)?
        .filter_map(|entry| entry.ok())
        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == "flac"))
        .collect();

    if flac_files.is_empty() {
        return Err(format!("在 {} 中未找到FLAC文件", extract_folder.display()).into());
    }

    let flac_path = flac_files[0].path();
    let song_title = flac_path.file_stem().unwrap().to_string_lossy();
    let music_dir = get_music_directory();
    fs::create_dir_all(music_dir)?;
    let output_path = music_dir.join(format!("{}.flac", song_title));

    let mut cmd = Command::new("ffmpeg");
    cmd.args(&["-y", "-i"]).arg(&flac_path);

    // 查找封面图片
    let jpg_files: Vec<_> = fs::read_dir(extract_folder)?
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            entry
                .path()
                .extension()
                .map_or(false, |ext| ext == "jpg" || ext == "jpeg" || ext == "png")
        })
        .collect();

    if !jpg_files.is_empty() {
        cmd.args(&["-i"]).arg(jpg_files[0].path());
        cmd.args(&["-map_metadata", "-1", "-map", "0:a", "-map", "1:v"]);
        cmd.args(&[
            "-c:a",
            "copy",
            "-c:v",
            "copy",
            "-disposition:v",
            "attached_pic",
        ]);
    } else {
        cmd.args(&["-c:a", "copy"]);
    }

    // 查找歌词文件
    let lrc_files: Vec<_> = fs::read_dir(extract_folder)?
        .filter_map(|entry| entry.ok())
        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == "lrc"))
        .collect();

    if !lrc_files.is_empty() {
        if let Ok(lyrics) = fs::read_to_string(&lrc_files[0].path()) {
            let escaped_lyrics = lyrics.replace('=', r"\=").replace(';', r"\;");
            cmd.args(&["-metadata", &format!("lyrics={}", escaped_lyrics)]);
        }
    }

    cmd.arg(&output_path);
    let output = cmd.output()?;

    if !output.status.success() {
        return Err(format!(
            "FFmpeg处理失败: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    print_success!(
        "🎵 文件处理完成: {}",
        output_path.file_name().unwrap().to_string_lossy()
    );
    fs::remove_dir_all(extract_folder)?;
    Ok(())
}

pub fn is_download_complete(file_path: &Path) -> Result<bool, Box<dyn std::error::Error>> {
    let initial_size = file_path.metadata()?.len();
    if initial_size == 0 {
        return Ok(false);
    }

    std::thread::sleep(Duration::from_millis(1000));
    let final_size = file_path.metadata()?.len();

    Ok(initial_size == final_size && final_size > 1000)
}

// ==================== 临时文件处理 ====================
pub fn find_audio_files_recursively(directory: &Path) -> Vec<PathBuf> {
    let audio_extensions = [
        ".wav", ".mp3", ".m4a", ".flac", ".aac", ".ogg", ".opus", ".wma", ".aiff", ".alac",
    ];

    let mut audio_files = Vec::new();

    if let Ok(entries) = fs::read_dir(directory) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.is_file() {
                if let Some(ext) = path.extension() {
                    if audio_extensions
                        .iter()
                        .any(|&audio_ext| ext == &audio_ext[1..])
                    {
                        audio_files.push(path);
                    }
                }
            } else if path.is_dir() {
                audio_files.extend(find_audio_files_recursively(&path));
            }
        }
    }

    audio_files
}

pub fn process_failed_downloads() -> Result<(), Box<dyn std::error::Error>> {
    let temp_dir = get_temp_directory();
    if !temp_dir.exists() {
        return Ok(());
    }

    print_info!("🔍 检查临时目录中的音频文件...");
    let audio_files = find_audio_files_recursively(temp_dir);

    if audio_files.is_empty() {
        print_info!("临时目录中未发现可用的音频文件");
        return Ok(());
    }

    print_success!("发现 {} 个音频文件，正在处理...", audio_files.len());
    let music_dir = get_music_directory();
    fs::create_dir_all(music_dir)?;

    let mut processed_count = 0;
    for audio_file in audio_files {
        let target_name = format!(
            "recovered_{}",
            audio_file.file_name().unwrap().to_string_lossy()
        );
        let mut target_path = music_dir.join(&target_name);

        let mut counter = 1;
        while target_path.exists() {
            let stem = audio_file.file_stem().unwrap().to_string_lossy();
            let ext = audio_file.extension().unwrap().to_string_lossy();
            let new_name = format!("recovered_{}_{}.{}", stem, counter, ext);
            target_path = music_dir.join(new_name);
            counter += 1;
        }

        if fs::rename(&audio_file, &target_path).is_ok() {
            print_success!(
                "✓ 已恢复: {}",
                target_path.file_name().unwrap().to_string_lossy()
            );
            processed_count += 1;
        }
    }

    if processed_count > 0 {
        print_success!("🎉 成功恢复 {} 个音频文件到音乐目录", processed_count);
    }

    Ok(())
}

pub fn handle_temp_directory_cleanup() -> Result<(), Box<dyn std::error::Error>> {
    let temp_dir = get_temp_directory();
    if !temp_dir.exists() {
        return Ok(());
    }

    process_failed_downloads()?;

    let remaining_files: Vec<_> = fs::read_dir(temp_dir)?
        .filter_map(|entry| entry.ok())
        .filter(|entry| entry.path().is_file())
        .collect();

    if !remaining_files.is_empty() {
        let music_dir = get_music_directory();
        let temp_backup_path = music_dir.join("temp_downloads_backup");

        if temp_backup_path.exists() {
            fs::remove_dir_all(&temp_backup_path)?;
        }

        if fs::rename(temp_dir, &temp_backup_path).is_ok() {
            print_warn!("⚠️  临时文件已备份到: {}", temp_backup_path.display());
            print_info!("请手动检查该目录中是否有需要的文件");
        }
    } else {
        fs::remove_dir_all(temp_dir).ok();
        print_info!("✓ 临时目录已清理");
    }

    Ok(())
}

pub fn cleanup_temp_directory() {
    let temp_dir = get_temp_directory();
    if temp_dir.exists() {
        if let Ok(entries) = fs::read_dir(temp_dir) {
            let has_files = entries.into_iter().any(|entry| entry.is_ok());
            if has_files {
                print_warn!(
                    "警告: 临时目录 '{}' 不为空，可能包含处理失败任务的残留文件",
                    temp_dir.display()
                );
            } else {
                fs::remove_dir_all(temp_dir).ok();
            }
        }
    }
}