use indicatif::{ProgressBar, ProgressStyle};
use regex::Regex;
use std::io::{self, Write};

use crate::env_setup::{get_music_directory, get_progress_manager};
use crate::{print_error, print_info, print_success, print_warn};

// ==================== 音质选择 ====================
pub fn select_audio_quality() -> String {
    println!("\n{}", "=".repeat(50));
    println!("🎵 音质选择 - 推荐设置");
    println!("{}", "=".repeat(50));
    println!("1: 超清母带 (SVIP) - 体积最大，需要SVIP会员");
    println!("2: 高解析度无损 (VIP) - 🌟 强烈推荐 🌟");
    println!("   • 音质标准：接近CD品质，满足绝大多数听音需求");
    println!("   • 文件体积：相比超清母带节省60-70%存储空间");
    println!("   • 兼容性强：支持更多设备和播放器");
    println!("{}", "=".repeat(50));

    loop {
        print!("请选择音质 (直接回车默认选择推荐的选项2): ");
        io::stdout().flush().unwrap();
        let mut input = String::new();
        io::stdin().read_line(&mut input).unwrap();
        let choice = input.trim();

        match choice {
            "" | "2" => {
                print_success!("✓ 已选择: 高解析度无损 (VIP) - 推荐选择！");
                return "高解析度无损(VIP)".to_string();
            }
            "1" => {
                print_info!("✓ 已选择: 超清母带 (SVIP)");
                print_warn!("注意: 如果解析失败会自动降级到高解析度无损");
                return "超清母带(SVIP)".to_string();
            }
            _ => {
                print_warn!("输入无效，请输入 1 或 2，或直接回车选择推荐选项");
            }
        }
    }
}

// ==================== 轻量级进度条 ====================
pub fn create_lightweight_main_progress_bar(total: usize) -> ProgressBar {
    let progress = get_progress_manager().add(ProgressBar::new(total as u64));
    progress.set_style(
        ProgressStyle::default_bar()
            .template(
                "{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {pos}/{len} {msg}",
            )
            .unwrap()
            .progress_chars("█▉▊▋▌▍▎▏ "),
    );
    progress.set_message("准备开始...");
    progress
}

pub fn create_lightweight_task_indicator(url: &str, current: usize, total: usize) -> String {
    let song_id = extract_song_id_from_url(url).unwrap_or_else(|| "Unknown".to_string());
    format!("🎵 [{}/{}] 歌曲ID: {}", current, total, song_id)
}

fn extract_song_id_from_url(url: &str) -> Option<String> {
    let re = Regex::new(r"song\?id=(\d+)").ok()?;
    re.captures(url)?.get(1).map(|m| m.as_str().to_string())
}

// ==================== 结果显示 ====================
pub fn display_final_results(total_urls: &[String], failed_urls: &std::collections::HashMap<String, String>) {
    let success_count = total_urls.len() - failed_urls.len();

    println!("\n{} ✅ 轻量级处理完成 {}", "=".repeat(20), "=".repeat(20));
    print_info!("📊 总计: {} 首歌曲", total_urls.len());
    print_success!("✅ 成功: {} 首", success_count);

    if !failed_urls.is_empty() {
        print_error!("❌ 失败: {} 首", failed_urls.len());
        println!("\n--- 失败的URL汇总 ---");
        for (i, (url, reason)) in failed_urls.iter().enumerate() {
            println!("{}. URL: {}", i + 1, url);
            println!("   错误: {}", reason);
        }
    } else {
        print_success!("🎉 所有URL均已成功处理！");
    }

    println!("\n{} 📁 文件位置信息 {}", "=".repeat(20), "=".repeat(20));
    print_info!("🎵 音乐文件保存在: {}", get_music_directory().display());

    if success_count > 0 {
        print_success!("✨ 下载的音频文件已包含:");
        print_success!("   • 封面图片（嵌入到音频文件中）");
        print_success!("   • 歌词信息（嵌入到metadata中）");
        print_success!("   • 完整的标签信息");
    }

    println!("\n{} ⚡ 架构优化 {}", "=".repeat(20), "=".repeat(20));
    print_info!("🔧 采用轻量级架构，减少系统开销");
    print_info!("📊 降低进度监控频率，避免浏览器干扰");
    print_info!("🛡️  基于稳定的debug.rs架构，确保可靠性");
    print_info!("🗂️  集中管理：所有程序文件都在下载目录下");
    print_info!("🔒 隐藏browser_data目录，避免用户混淆");
    print_info!("🧹 自动清理：启动和退出时智能清理遗留文件");

    println!("{}", "=".repeat(70));
}

pub fn display_program_header() {
    println!("{}", "=".repeat(70));
    print_success!("🎵 网易云音乐统一下载器 - 轻量级版本");
    println!("集中管理 | 隐藏文件夹 | 自动清理 | 优化性能");
    print_info!("📁 音乐保存位置: {}", get_music_directory().display());
    print_info!(
        "🔒 浏览器数据位置: {} (隐藏)",
        crate::env_setup::get_current_browser_data_dir().display()
    );
    println!("{}", "=".repeat(70));
}
