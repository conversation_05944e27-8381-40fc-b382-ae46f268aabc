{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 8276155916380437441, "path": 6289995541683000688, "deps": [[2828590642173593838, "cfg_if", false, 18205415188767257961]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-52bf6f39edf5542e/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}