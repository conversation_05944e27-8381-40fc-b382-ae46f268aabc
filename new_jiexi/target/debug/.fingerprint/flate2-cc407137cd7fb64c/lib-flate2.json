{"rustc": 12610991425282158916, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 8276155916380437441, "path": 5980915223080075204, "deps": [[5466618496199522463, "crc32fast", false, 1086201972499767643], [7636735136738807108, "miniz_oxide", false, 18259543471569428554]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-cc407137cd7fb64c/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}