{"$message_type":"diagnostic","message":"constant `CONCURRENT_LIMIT` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/config.rs","byte_start":32,"byte_end":48,"line_start":2,"line_end":2,"column_start":11,"column_end":27,"is_primary":true,"text":[{"text":"pub const CONCURRENT_LIMIT: usize = 2;","highlight_start":11,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `CONCURRENT_LIMIT` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config.rs:2:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub const CONCURRENT_LIMIT: usize = 2;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":1161,"byte_end":1194,"line_start":35,"line_end":35,"column_start":41,"column_end":74,"is_primary":true,"text":[{"text":"        if let Some(browser_data_dir) = CURRENT_BROWSER_DATA_DIR.as_ref() {","highlight_start":41,"highlight_end":74}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:35:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(browser_data_dir) = CURRENT_BROWSER_DATA_DIR.as_ref() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":3013,"byte_end":3031,"line_start":86,"line_end":86,"column_start":30,"column_end":48,"is_primary":true,"text":[{"text":"    let music_dir = unsafe { MUSIC_DIR.as_ref().unwrap() };","highlight_start":30,"highlight_end":48}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:86:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let music_dir = unsafe { MUSIC_DIR.as_ref().unwrap() };\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":8877,"byte_end":8895,"line_start":254,"line_end":254,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"    unsafe { MUSIC_DIR.as_ref().unwrap() }","highlight_start":14,"highlight_end":32}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:254:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { MUSIC_DIR.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":8973,"byte_end":8990,"line_start":258,"line_end":258,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"    unsafe { TEMP_DIR.as_ref().unwrap() }","highlight_start":14,"highlight_end":31}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:258:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { TEMP_DIR.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":9076,"byte_end":9101,"line_start":262,"line_end":262,"column_start":14,"column_end":39,"is_primary":true,"text":[{"text":"    unsafe { PROGRESS_MANAGER.as_ref().unwrap() }","highlight_start":14,"highlight_end":39}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:262:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { PROGRESS_MANAGER.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":9189,"byte_end":9222,"line_start":266,"line_end":266,"column_start":14,"column_end":47,"is_primary":true,"text":[{"text":"    unsafe { CURRENT_BROWSER_DATA_DIR.as_ref().unwrap() }","highlight_start":14,"highlight_end":47}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:266:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { CURRENT_BROWSER_DATA_DIR.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 7 warnings emitted\u001b[0m\n\n"}
