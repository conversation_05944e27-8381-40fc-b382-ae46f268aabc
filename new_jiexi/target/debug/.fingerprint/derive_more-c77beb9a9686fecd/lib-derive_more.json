{"rustc": 15497389221046826682, "features": "[\"add\", \"add_assign\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 3033921117576893, "path": 4188931261856679396, "deps": [[3060637413840920116, "proc_macro2", false, 14056192580875739085], [4974441333307933176, "syn", false, 11413032947016716687], [17990358020177143287, "quote", false, 9507505860812555772]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-c77beb9a9686fecd/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}