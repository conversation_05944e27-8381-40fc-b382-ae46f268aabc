{"rustc": 12610991425282158916, "features": "[\"add\", \"add_assign\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 3033921117576893, "path": 4188931261856679396, "deps": [[3060637413840920116, "proc_macro2", false, 3118315257292581458], [4974441333307933176, "syn", false, 3596148971329198544], [17990358020177143287, "quote", false, 4536169686237817344]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-8255f1398d8e9a61/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}