{"rustc": 15497389221046826682, "features": "[\"lib_has_std\"]", "declared_features": "[\"alloc\", \"clippy\", \"lib_has_std\"]", "target": 15229808779680689443, "profile": 3033921117576893, "path": 6775134317405571581, "deps": [[4003231138667150418, "derive_builder_core", false, 15767179732970359472], [4974441333307933176, "syn", false, 11413032947016716687]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_builder_macro-1252faa424e65af4/dep-lib-derive_builder_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}