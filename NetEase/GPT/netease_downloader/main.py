import asyncio
import os
import shutil
import subprocess
import zipfile
from pathlib import Path
from typing import List, Dict
from playwright.async_api import async_playwright, Error as PlaywrightError
import urllib.parse
import os
import sys

# ----------------- 为PyInstaller添加的代码段 开始 -----------------
# 判断程序是否被PyInstaller打包
if getattr(sys, 'frozen', False):
    # 如果是打包状态，设置PLAYWRIGHT_BROWSERS_PATH环境变量
    # sys._MEIPASS是PyInstaller在运行时创建的临时目录，我们的资源会被解压到这里
    browsers_path = os.path.join(sys._MEIPASS, "ms-playwright")
    os.environ['PLAYWRIGHT_BROWSERS_PATH'] = browsers_path
# ----------------- 为PyInstaller添加的代码段 结束 -----------------

# 在设置完环境变量后，再导入playwright
from playwright.async_api import async_playwright, Error as PlaywrightError


# --- ANSI 终端颜色代码，用于美化日志输出 ---
class LogColors:
    SUCCESS = '\033[92m'  # 绿色，用于成功信息
    INFO = '\033[94m'  # 蓝色，用于普通信息
    WARN = '\033[93m'  # 黄色，用于警告信息
    ERROR = '\033[91m'  # 红色，用于错误信息
    BLINK = '\033[5m'  # 闪烁，用于高亮强调失败任务
    RESET = '\033[0m'  # 重置终端颜色


# --- 全局配置区 ---
# 设置同一时间内并发执行的最大下载任务数量，以防止网络拥塞或IP被封锁
CONCURRENT_LIMIT = 2
# 为每个失败的URL设置最大重试次数（总尝试次数 = 1次初始尝试 + MAX_RETRIES次重试）
MAX_RETRIES = 2
# 单个文件下载过程的超时时间（秒），为大文件和慢网络预留足够时间
DOWNLOAD_TIMEOUT = 480  # 8分钟
# 目标解析网站的URL
TARGET_PAGE_URL = "https://api.toubiec.cn/wyapi.html"
# 最终整合完毕的音乐文件的存放目录
DOWNLOAD_DIR = Path("downloads")
# 下载过程中的临时文件存放目录（存放zip压缩包和解压后的文件夹）
TEMP_DIR = Path("temp_downloads")


class ProcessingError(Exception):
    """
    自定义异常类。
    在处理流程中，无论捕获到何种具体类型的错误（如Playwright、网络、文件操作等），
    都将其包装为此异常类型再向上抛出，便于主逻辑中统一捕获和处理。
    """
    pass


def clean_url(raw_url: str) -> str | None:
    """
    清洗并验证输入的网易云音乐URL。
    - 验证域名是否为 music.163.com。
    - 提取核心的歌曲 'id' 参数，去除其他所有无关参数（如用户指纹、分享来源等）。
    - 返回一个干净、标准的歌曲URL。
    """
    try:
        parsed_url = urllib.parse.urlparse(raw_url)
        if "music.163.com" not in parsed_url.netloc:
            print(f"{LogColors.WARN}警告: 不支持的URL域名: {raw_url}{LogColors.RESET}")
            return None
        query_params = urllib.parse.parse_qs(parsed_url.query)
        song_id = query_params.get('id', [None])[0]
        if not song_id:
            print(f"{LogColors.WARN}警告: 在URL中未找到歌曲ID: {raw_url}{LogColors.RESET}")
            return None
        return f"https://music.163.com/song?id={song_id}"
    except Exception as e:
        print(f"{LogColors.ERROR}错误: 解析URL '{raw_url}' 时发生错误: {e}{LogColors.RESET}")
        return None


def select_audio_quality() -> str:
    """
    提供交互式命令行选项，让用户选择期望下载的音质。
    返回一个与目标网站前端选项完全匹配的文本字符串。
    """
    print("\n--- 请选择本次任务的下载音质 ---")
    print("1: 超清母带 (SVIP - 脚本会优先选择此项，若不可用则自动降级)")
    print("2: 高解析度无损 (VIP)")
    while True:
        choice = input("请输入选项 (1/2): ").strip()
        if choice == '1': return '超清母带(SVIP)'
        if choice == '2': return '高解析度无损(VIP)'
        print(f"{LogColors.WARN}输入无效，请重新输入。{LogColors.RESET}")


def post_process_folder(extract_folder: Path):
    """
    核心后处理函数：使用FFmpeg将音频、封面、歌词整合到一个文件中。
    优化点：此函数会先清除源FLAC文件中的所有元数据，再写入新的封面和歌词，确保最终文件只有一套元数据。
    """
    print(f"{LogColors.INFO}信息: 开始后处理文件夹: {extract_folder}{LogColors.RESET}")
    try:
        flac_files = list(extract_folder.glob('*.flac'))
        if not flac_files:
            raise ProcessingError(f"在 {extract_folder} 中未找到 .flac 文件。")

        flac_path = flac_files[0]
        song_title = flac_path.stem
        DOWNLOAD_DIR.mkdir(exist_ok=True)
        output_path = DOWNLOAD_DIR / f"{song_title}.flac"

        # --- 构建FFmpeg命令 ---
        ffmpeg_cmd = ['ffmpeg', '-y', '-i', str(flac_path)]

        jpg_files = list(extract_folder.glob('*.jpg'))
        lrc_files = list(extract_folder.glob('*.lrc'))

        if jpg_files:
            ffmpeg_cmd.extend(['-i', str(jpg_files[0])])

        # 关键步骤：-map_metadata -1 用于清除所有输入文件的元数据流。
        # 这样可以保证旧的、可能重复的封面或歌词标签被彻底移除。
        ffmpeg_cmd.extend(['-map_metadata', '-1'])

        # 映射流：-map 0:a 指定使用第一个输入（FLAC文件）的音频流。
        ffmpeg_cmd.extend(['-map', '0:a'])
        if jpg_files:
            # -map 1:v 指定使用第二个输入（JPG文件）的视频流（即封面）。
            ffmpeg_cmd.extend(['-map', '1:v'])

        # 设置编码器：对音频和视频（封面）都使用 'copy'，避免不必要的重编码，速度最快且无损。
        ffmpeg_cmd.extend(['-c:a', 'copy'])
        if jpg_files:
            # -disposition:v attached_pic 将视频流标记为附加图片（封面）。
            ffmpeg_cmd.extend(['-c:v', 'copy', '-disposition:v', 'attached_pic'])

        if lrc_files:
            try:
                with open(lrc_files[0], 'r', encoding='utf-8') as f:
                    # 读取歌词内容，并对FFmpeg元数据中的特殊字符'='和';'进行转义。
                    lyrics_content = f.read().replace('=', '\\=').replace(';', '\\;')
                # 将处理后的歌词作为新的元数据写入。
                ffmpeg_cmd.extend(['-metadata', f'lyrics={lyrics_content}'])
            except Exception as e:
                print(f"{LogColors.WARN}警告: 读取歌词文件 {lrc_files[0].name} 失败: {e}{LogColors.RESET}")

        # 命令的最后一部分是输出文件路径。
        ffmpeg_cmd.append(str(output_path))

        print(f"{LogColors.INFO}信息: 正在执行FFmpeg命令...{LogColors.RESET}")
        result = subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True, encoding='utf-8')

        print(f"{LogColors.SUCCESS}成功: 文件整合完毕 -> {output_path}{LogColors.RESET}")
        # 成功后，清理临时解压文件夹。
        shutil.rmtree(extract_folder)
        print(f"{LogColors.INFO}信息: 已删除临时文件夹: {extract_folder.name}{LogColors.RESET}")

    except FileNotFoundError:
        raise ProcessingError("错误: 未找到 'ffmpeg' 命令。请确保 FFmpeg 已安装并已添加到系统环境变量 PATH 中。")
    except subprocess.CalledProcessError as e:
        raise ProcessingError(f"错误: ffmpeg 处理 {song_title} 时出错: {e.stderr}")
    except Exception as e:
        raise ProcessingError(f"错误: 后处理 {extract_folder} 时发生未知错误: {e}")


async def process_single_url(context, url: str, quality_text: str):
    """
    处理单个URL的核心业务逻辑。
    包括页面导航、交互、监听下载、文件解压和调用后处理。
    任何步骤失败都会抛出 ProcessingError 异常。
    """
    print(f"{LogColors.INFO}信息: 开始处理任务: {url}{LogColors.RESET}")
    page = await context.new_page()

    # 创建一个 asyncio.Future 对象，它将作为一个信号，等待下载完成。
    # 下载事件监听器 `on_download` 会在下载成功或失败时设置此 Future 的结果。
    download_future = asyncio.Future()

    async def on_download(download):
        """Playwright的下载事件监听器，一旦下载开始就会被调用。"""
        try:
            TEMP_DIR.mkdir(exist_ok=True)
            zip_path = TEMP_DIR / download.suggested_filename
            print(f"{LogColors.INFO}信息: 触发下载事件，开始保存文件到 -> {zip_path}{LogColors.RESET}")
            await download.save_as(zip_path)
            print(f"{LogColors.SUCCESS}成功: 文件已保存: {zip_path.name}{LogColors.RESET}")
            # 下载成功，将文件路径作为结果设置给Future，以通知主流程。
            download_future.set_result(zip_path)
        except Exception as e:
            # 下载过程中发生任何错误，将异常设置给Future。
            download_future.set_exception(ProcessingError(f"保存下载文件时失败: {e}"))

    # 将上面定义的 `on_download` 函数注册为页面的 'download' 事件的回调。
    page.on("download", on_download)

    try:
        # --- 页面交互 ---
        await page.goto(TARGET_PAGE_URL, timeout=60000)
        await page.wait_for_selector("h5:has-text('网易云无损解析')", state='visible', timeout=15000)
        await page.locator("[placeholder=\"请输入网易云音乐链接\"]").fill(url)
        await page.locator(".ep-select__wrapper").first.click()
        try:
            # 优先尝试用户选择的音质
            await page.locator(f"li:has-text('{quality_text}')").click(timeout=3000)
        except PlaywrightError:
            # 如果首选音质不可用（例如歌曲没有母带级别），自动降级到VIP无损。
            print(
                f"{LogColors.WARN}警告: 音质 '{quality_text}' 不可用，自动为 {url} 降级到 '高解析度无损(VIP)'{LogColors.RESET}")
            await page.locator("li:has-text('高解析度无损(VIP)')").click()

        await page.locator(".ep-select__wrapper").nth(1).click()
        await page.locator("li:has-text('单曲')").click()
        await page.locator("button:has-text('立即解析')").click()
        await page.locator("button:has-text('确认')").click()

        # --- 触发并等待下载 ---
        download_button = page.locator("button:has-text('点击下载')")
        await download_button.wait_for(state='visible', timeout=60000)
        await download_button.click()

        # 等待 `on_download` 函数完成（即下载完成），并设置一个总的下载超时。
        zip_path = await asyncio.wait_for(download_future, timeout=DOWNLOAD_TIMEOUT)

        # --- 解压与后处理 ---
        extract_folder = zip_path.with_suffix('')
        print(f"{LogColors.INFO}信息: 开始解压: {zip_path.name} -> {extract_folder.name}{LogColors.RESET}")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_folder)
        zip_path.unlink()  # 解压后删除zip包

        post_process_folder(extract_folder)

    except Exception as e:
        # 捕获此任务中的任何异常，包装后抛出，交由 main 函数的重试逻辑处理。
        raise ProcessingError(f"处理 {url} 失败: {e}")
    finally:
        # 无论成功与否，都确保移除事件监听器并关闭页面，避免资源泄露。
        page.remove_listener("download", on_download)
        await page.close()


async def main():
    """
    主函数，负责整个程序的调度、任务分发和失败重试逻辑。
    """
    urls_file = Path("urls.txt")
    raw_urls = []
    if urls_file.exists():
        print(f"{LogColors.INFO}信息: 发现 'urls.txt' 文件，准备进行批量处理。{LogColors.RESET}")
        with open(urls_file, 'r', encoding='utf-8') as f:
            raw_urls = [line.strip() for line in f if line.strip()]
    else:
        print(f"信息: 未找到 'urls.txt'。")
        url = input("请输入单个网易云音乐歌曲链接: ").strip()
        if url: raw_urls.append(url)

    if not raw_urls:
        print(f"{LogColors.WARN}警告: 没有需要处理的URL。程序退出。{LogColors.RESET}")
        return

    # 清洗所有原始URL
    urls_to_process = [cleaned for url in raw_urls if (cleaned := clean_url(url)) is not None]
    if not urls_to_process:
        print(f"{LogColors.WARN}警告: 清洗后没有有效的URL。程序退出。{LogColors.RESET}")
        return

    # 让用户选择音质
    selected_quality = select_audio_quality()

    # 存储最终失败的任务，键为URL，值为最后一次的错误信息。
    failed_tasks: Dict[str, str] = {}

    # --- 重试循环 ---
    # 总共尝试 MAX_RETRIES + 1 次
    for attempt in range(MAX_RETRIES + 1):
        if not urls_to_process:
            break  # 如果没有需要处理的URL了，提前结束循环

        if attempt > 0:
            print(f"\n{LogColors.WARN}--- 开始重试失败的任务 (第 {attempt}/{MAX_RETRIES} 次重试) ---{LogColors.RESET}")
            await asyncio.sleep(5)  # 重试前等待5秒

        print(
            f"\n{LogColors.INFO}信息: 开始处理 {len(urls_to_process)} 个URL。并发数: {CONCURRENT_LIMIT}。{LogColors.RESET}")

        # 使用信号量来控制并发数量
        semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)

            async def run_with_semaphore(url):
                """一个包装函数，确保每个任务都在信号量的控制下运行。"""
                async with semaphore:
                    try:
                        # 每个任务都创建独立的浏览器上下文，确保隔离性。
                        context = await browser.new_context(accept_downloads=True)
                        await process_single_url(context, url, selected_quality)
                        return url, None  # 成功时返回None作为错误信息
                    except Exception as e:
                        # 失败时，高亮打印错误，并返回错误信息。
                        print(f"\n{LogColors.ERROR}{LogColors.BLINK}---> 任务失败: {url} <---{LogColors.RESET}")
                        print(f"{LogColors.ERROR}{str(e)}{LogColors.RESET}\n")
                        return url, str(e)  # 返回URL和错误信息
                    finally:
                        # 确保任务结束后关闭其上下文。
                        if 'context' in locals() and context:
                            await context.close()

            tasks = [run_with_semaphore(url) for url in urls_to_process]
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            await browser.close()

        # --- 处理本轮结果，准备下一轮重试 ---
        urls_to_process.clear()
        for url, error_message in results:
            if error_message:
                urls_to_process.append(url)  # 将失败的URL添加到下一轮的处理列表
                failed_tasks[url] = error_message  # 更新或记录这个URL的最新错误信息

    # --- 最终总结 ---
    print("\n--- 所有任务处理完毕 ---")
    # 如果 urls_to_process 列表在所有重试后仍然不为空，说明这些是最终失败的任务。
    if failed_tasks and urls_to_process:
        print(f"\n{LogColors.ERROR}{LogColors.BLINK}--- 永久失败的URL汇总 ---{LogColors.RESET}")
        for i, url in enumerate(urls_to_process):
            reason = failed_tasks.get(url, "未知错误")
            print(f"{i + 1}. URL: {url}")
            print(f"   最终错误: {reason}\n")
    else:
        print(f"{LogColors.SUCCESS}所有URL均已成功处理！{LogColors.RESET}")

    if any(TEMP_DIR.iterdir()):
        print(
            f"{LogColors.WARN}\n警告: 临时目录 '{TEMP_DIR}' 不为空，可能包含处理失败任务的残留文件，请手动检查。{LogColors.RESET}")


if __name__ == "__main__":
    asyncio.run(main())