# -*- coding: utf-8 -*-

"""
网易云音乐内容解析工具 (V7 - 鲁棒性修正版)

功能:
1. 通过命令行交互式地接收用户输入的歌单(playlist)或专辑(album)的URL。
2. 自动裁切URL中多余的分享参数（指纹），并智能识别URL类型。
3. 调用最优解析逻辑：
   - 专辑(Album): 直接解析HTML页面，无需加密API。
   - 歌单(Playlist): 调用weapi接口，通过两步获取完整的歌曲列表。
4. 【修正】只要成功获取歌曲列表，就优先确保生成纯净的 `urls_gotten.txt`。
5. 遍历所有歌曲，为每一首歌曲单独获取其下载链接（URL）。
6. 对网络请求、无版权等异常情况进行捕获和实时警告。
7. 输出两个文件到“解析结果”文件夹。
8. 在任务结束时，于命令行打印失败任务的总结报告。
"""

import json
import os
import random
import re
import string
import sys
import time
from base64 import b64encode

try:
    import requests
    from bs4 import BeautifulSoup
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import pad
except ImportError:
    print("错误：缺少必要的库。请通过以下命令安装：")
    print("pip install requests pycryptodome beautifulsoup4 lxml")
    sys.exit(1)

# =========================================================================
# 全局配置与常量
# =========================================================================
QUALITY_LEVEL = "lossless"  # 可在此处预设音质: "standard", "higher", "exhigh", "lossless"
MODULUS = "00e0b509f6259df8642dbc35662901477df22677ec152b5ff68ace615bb7b725152b3ab17a876aea8a5aa76d2e417629ec4ee341f56135fccf695280104e0312ecbda92557c93870114af6c9d05c4f7f0c3685b7a46bee255932575cce10b424d813cfe4875d3e82047b97ddef52741d546b8e289dc6935b3ece0462db0a22b8e7"
NONCE = "0CoJUm6Qyw8W8jud"
PUBKEY = "010001"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Referer': 'https://music.163.com/',
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept-Language': 'zh-CN,zh;q=0.9',
}


# =========================================================================
# 核心加密与数据获取函数 (保持不变)
# =========================================================================
def generate_random_key(length: int) -> str:
    seed = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return "".join(random.choices(seed, k=length))


def aes_encrypt(data: str, key: str) -> str:
    iv = b"0102030405060708"
    data_bytes = data.encode('utf-8')
    key_bytes = key.encode('utf-8')
    cipher = AES.new(key_bytes, AES.MODE_CBC, iv)
    padded_data = pad(data_bytes, AES.block_size)
    encrypted_data = cipher.encrypt(padded_data)
    return b64encode(encrypted_data).decode('utf-8')


def rsa_encrypt(text: str, pub_key: str, modulus: str) -> str:
    text_reversed_bytes = text.encode('utf-8')[::-1]
    text_int = int(text_reversed_bytes.hex(), 16)
    pub_key_int = int(pub_key, 16)
    modulus_int = int(modulus, 16)
    encrypted_int = pow(text_int, pub_key_int, modulus_int)
    return format(encrypted_int, 'x').zfill(256)


def get_weapi_params(payload: dict) -> dict:
    payload_str = json.dumps(payload)
    secret_key = generate_random_key(16)
    params = aes_encrypt(payload_str, NONCE)
    params = aes_encrypt(params, secret_key)
    enc_sec_key = rsa_encrypt(secret_key, PUBKEY, MODULUS)
    return {"params": params, "encSecKey": enc_sec_key}


def fetch_album_data(album_id: str) -> tuple:
    print("识别为专辑任务，采用HTML解析模式...")
    album_url = f"https://music.163.com/album?id={album_id}"
    response = requests.get(album_url, headers=HEADERS)
    response.raise_for_status()
    soup = BeautifulSoup(response.text, 'lxml')
    album_name_tag = soup.find('h2', class_='f-ff2')
    album_name = album_name_tag.text.strip() if album_name_tag else "Unknown_Album"
    textarea = soup.find('textarea', id='song-list-pre-data')
    if not textarea:
        raise ValueError("在专辑页面HTML中未找到 'song-list-pre-data'，可能页面结构已更新。")
    tracks = json.loads(textarea.string)
    return album_name, tracks


def fetch_playlist_data(playlist_id: str) -> tuple:
    print("识别为歌单任务，采用WEAPI加密接口模式...")
    playlist_api_url = "https://music.163.com/weapi/v6/playlist/detail?csrf_token="
    playlist_payload = {"id": playlist_id, "n": 100000, "s": 8, "csrf_token": ""}
    encrypted_playlist_data = get_weapi_params(playlist_payload)
    response = requests.post(playlist_api_url, headers=HEADERS, data=encrypted_playlist_data, timeout=10)
    response.raise_for_status()
    result = response.json()
    task_name = result.get("playlist", {}).get("name", "Unknown_Playlist")
    track_ids = result.get("playlist", {}).get("trackIds", [])
    if not track_ids:
        return task_name, []
    print(f"✅ 成功获取歌单:《{task_name}》，检测到共 {len(track_ids)} 首歌曲。")
    print("开始获取完整的歌曲信息列表...")
    song_ids = [item['id'] for item in track_ids]
    url_song_detail = "https://music.163.com/weapi/v3/song/detail"
    chunk_size = 500
    tracks = []
    for i in range(0, len(song_ids), chunk_size):
        chunk = song_ids[i:i + chunk_size]
        payload = {"c": json.dumps([{"id": song_id} for song_id in chunk]), "csrf_token": ""}
        encrypted_data = get_weapi_params(payload)
        response = requests.post(url_song_detail, headers=HEADERS, data=encrypted_data, timeout=10)
        tracks.extend(response.json().get('songs', []))
        print(f"  > 已成功获取 {len(tracks)}/{len(song_ids)} 首歌曲的详细信息...")
        time.sleep(random.uniform(0.5, 1.0))
    return task_name, tracks


def get_song_download_info(song_id: int, quality: str) -> dict:
    url = "https://music.163.com/weapi/song/enhance/player/url/v1"
    payload = {"ids": f"[{song_id}]", "level": quality, "encodeType": "flac", "csrf_token": ""}
    response = requests.post(url, headers=HEADERS, data=get_weapi_params(payload), timeout=10)
    response.raise_for_status()
    return response.json().get("data", [{}])[0]


# =========================================================================
# 主程序 (逻辑修正)
# =========================================================================
def process_task(task_type: str, task_id: str, quality_level: str):
    """根据任务类型处理任务，并确保文件写入的健壮性。"""
    try:
        if task_type == 'playlist':
            task_name, tracks = fetch_playlist_data(task_id)
        elif task_type == 'album':
            task_name, tracks = fetch_album_data(task_id)
        else:
            print(f"未知的任务类型: {task_type}")
            return

        if not tracks:
            print(f"❌ 未能从《{task_name}》中获取到任何歌曲信息。")
            return

        print(f"\n✅ 初始信息获取完毕:《{task_name}》，共 {len(tracks)} 首歌曲。")

    except Exception as e:
        print(f"\n获取 {task_type} 详情时发生致命错误！❌\n错误: {e}")
        return

    # --- 【逻辑修正】只要获取到歌曲列表，就立即生成纯净URL列表并写入文件 ---
    output_dir = "解析结果"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    simple_url_results = [f"https://music.163.com/song?id={track.get('id')}" for track in tracks]
    simple_output_filepath = os.path.join(output_dir, "urls_gotten.txt")

    try:
        with open(simple_output_filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(simple_url_results))
        print(f"✅ (优先任务) 纯净链接文件已成功生成: {os.path.abspath(simple_output_filepath)}")
    except IOError as e:
        print(f"❌ 写入纯净链接文件时发生错误: {e}")

    # --- 继续获取详细下载链接并准备详细报告 ---
    print("\n现在开始逐一获取每首歌曲的详细下载链接...")
    failed_songs = []
    detailed_results = []

    for index, track in enumerate(tracks):
        song_id = track.get("id")
        song_name = track.get("name", "Unknown Song")
        artist_name = ", ".join([artist.get('name', '') for artist in track.get("ar", [])])

        print(f"  ({index + 1}/{len(tracks)}) 正在处理: {song_name} - {artist_name}")

        try:
            song_info = get_song_download_info(song_id, quality=quality_level)
            download_url = song_info.get('url')
            if not download_url:
                raise ValueError("获取到的URL为空 (可能无版权或为VIP歌曲)")

            info_str = f"歌曲: {song_name}\n歌手: {artist_name}\nID: {song_id}\n音质: {song_info.get('level', 'N/A')}\n格式: {song_info.get('type', 'N/A')}\n下载URL: {download_url}\n{'-' * 40}\n"
            detailed_results.append(info_str)
        except Exception as e:
            warning_msg = f"  ⚠️ 警告: 处理歌曲 '{song_name}' (ID: {song_id}) 时失败。"
            print(warning_msg)
            failed_songs.append({"name": song_name, "artist": artist_name, "id": song_id, "reason": str(e)})
            detailed_results.append(
                f"歌曲: {song_name}\n歌手: {artist_name}\nID: {song_id}\n状态: 获取失败\n原因: {e}\n{'-' * 40}\n")

        time.sleep(random.uniform(0.5, 1.2))

    # --- 写入详细报告文件 ---
    safe_task_name = "".join(c for c in task_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
    detailed_output_filepath = os.path.join(output_dir, f"{safe_task_name}.txt")
    try:
        with open(detailed_output_filepath, 'w', encoding='utf-8') as f:
            f.write(f"{task_type.capitalize()}: {task_name}\nID: {task_id}\n共 {len(tracks)} 首歌曲\n\n")
            f.writelines(detailed_results)
        print(f"✅ 详细信息文件已成功生成: {os.path.abspath(detailed_output_filepath)}")
    except IOError as e:
        print(f"❌ 写入详细信息文件时发生错误: {e}")

    print("\n🎉 所有任务处理完毕！")

    # --- 失败任务总结报告 ---
    if failed_songs:
        print("\n" + "=" * 20 + " 失败任务总结 " + "=" * 20)
        print(f"共有 {len(failed_songs)} 首歌曲获取下载链接失败，详情如下：")
        for i, failure in enumerate(failed_songs):
            print(f"  {i + 1}. 歌名: {failure['name']} | 歌手: {failure['artist']} | ID: {failure['id']}")
            print(f"     原因: {failure['reason']}")
        print("=" * 54)


def parse_url(url: str) -> tuple:
    """清理并解析URL，确保始终返回两个值。"""
    cleaned_url = url.split('&')[0]
    match = re.search(r'music\.163\.com/(?:#/)?(album|playlist)\?id=(\d+)', cleaned_url)
    if match:
        return match.groups()
    return None, None


# =========================================================================
#                           程序入口 (交互式)
# =========================================================================
if __name__ == "__main__":
    print("欢迎使用网易云音乐内容解析工具 (V7 - 鲁棒性修正版)")
    print(f"默认音质设置为: {QUALITY_LEVEL} (可在脚本顶部修改)")
    print("-" * 50)

    while True:
        raw_url = input("\n请输入网易云音乐的歌单或专辑URL (输入 'exit' 退出): \n> ")

        if raw_url.strip().lower() == 'exit':
            print("程序已退出。")
            break

        if not raw_url.strip():
            continue

        task_type, task_id = parse_url(raw_url)

        if not task_type or not task_id:
            print("  ❌ 错误：无法从输入的URL中解析出有效的类型和ID。")
            print("     请确保URL格式为: https://music.163.com/playlist?id=... 或 https://music.163.com/album?id=...")
            continue

        print(f"  > 已自动裁切指纹，处理链接: {raw_url.split('&')[0]}")

        process_task(task_type, task_id, QUALITY_LEVEL)
        print("-" * 50)