import pandas as pd
import argparse
import os
import numpy as np


def analyze_row(row):
    """
    对 DataFrame 的每一行应用启发式规则，增强数据验证
    """
    notes = []
    status = "质量良好"
    is_suspect = False

    # 添加数据完整性检查
    missing_fields = []
    critical_fields = ['rmsDbAbove18k', 'lra']

    # 检查关键字段是否缺失或为0
    for field in critical_fields:
        if field in row:
            if pd.isna(row[field]) or row[field] == 0.0:
                missing_fields.append(field)
        else:
            missing_fields.append(field)

    # 检查峰值字段（支持两种字段名）
    peak_field = None
    if 'peakAmplitudeDb' in row:
        peak_field = 'peakAmplitudeDb'
    elif 'peakAmplitude' in row:
        peak_field = 'peakAmplitude'
    else:
        missing_fields.append('peakAmplitude/peakAmplitudeDb')

    if missing_fields:
        notes.append(f"关键数据缺失: {', '.join(missing_fields)}，分析可能不准确。")
        if len(missing_fields) >= 2:
            status = "数据不完整"
            return status, " | ".join(notes)

    # 优化18kHz截止检测 - 使用更严格的阈值
    if 'rmsDbAbove18k' in row and pd.notna(row['rmsDbAbove18k']):
        if row['rmsDbAbove18k'] < -85:
            notes.append("频谱在约 18kHz 处存在硬性截止 (高度疑似伪造/升频)。")
            status = "可疑 (伪造)"
            is_suspect = True
        elif row['rmsDbAbove18k'] < -80:
            notes.append("频谱在 18kHz 处能量较低，可能存在软性截止。")
            if not is_suspect:
                status = "疑似处理"

    # 修正峰值检测 - 支持两种字段格式
    if peak_field and pd.notna(row[peak_field]):
        # 如果是dB值，检查是否接近0dB
        if peak_field == 'peakAmplitudeDb' and row[peak_field] >= -0.1:
            notes.append("存在严重数字削波风险 (峰值接近0dB)。")
            if not is_suspect:
                status = "已削波"
        # 如果是线性值，检查是否接近1.0
        elif peak_field == 'peakAmplitude' and row[peak_field] >= 0.999:
            notes.append("存在严重数字削波风险。")
            if not is_suspect:
                status = "已削波"

    # LRA分析优化
    if 'lra' in row and pd.notna(row['lra']) and row['lra'] > 0:
        if row['lra'] < 3.0:
            notes.append(f"动态范围极低 (LRA: {row['lra']:.1f} LU)，严重过度压缩。")
            if not is_suspect:
                status = "严重压缩"
        elif row['lra'] < 6.0:
            notes.append(f"动态范围过低 (LRA: {row['lra']:.1f} LU)，可能过度压缩。")
            if not is_suspect:
                status = "低动态"
        elif row['lra'] > 20.0:
            notes.append(f"动态范围过高 (LRA: {row['lra']:.1f} LU)，可能需要压缩处理。")

    if not notes:
        notes.append("未发现明显的硬性技术问题。")

    return status, " | ".join(notes)


def map_to_score(value, in_min, in_max, out_min=0, out_max=1):
    """一个通用的值范围映射函数，增强异常处理"""
    if pd.isna(value) or value is None:
        return 0

    # 确保值在输入范围内
    value = max(in_min, min(float(value), in_max))

    # 避免除零错误
    if in_max == in_min:
        return out_min

    return out_min + (value - in_min) * (out_max - out_min) / (in_max - in_min)


def calculate_quality_score(row):
    """
    优化后的质量评分算法，处理数据缺失情况
    """
    # 权重配置
    MAX_SCORE_INTEGRITY = 40
    MAX_SCORE_DYNAMICS = 30
    MAX_SCORE_SPECTRUM = 30

    # 数据完整性惩罚
    completeness_penalty = 0
    critical_fields = ['rmsDbAbove18k', 'lra']

    # 检查峰值字段
    peak_field = None
    if 'peakAmplitudeDb' in row:
        peak_field = 'peakAmplitudeDb'
        critical_fields.append('peakAmplitudeDb')
    elif 'peakAmplitude' in row:
        peak_field = 'peakAmplitude'
        critical_fields.append('peakAmplitude')

    missing_count = 0
    for field in critical_fields:
        if field not in row or pd.isna(row.get(field, np.nan)) or row.get(field, 0) == 0.0:
            missing_count += 1

    completeness_penalty = missing_count * 10  # 每缺失一个关键字段扣10分

    # 1. 技术完整性评分 (0-40分)
    integrity_score = 0

    # 频谱完整性 (0-25分)
    if 'rmsDbAbove18k' in row and pd.notna(row['rmsDbAbove18k']) and row['rmsDbAbove18k'] != 0:
        if row['rmsDbAbove18k'] >= -70:
            integrity_score += 25
        elif row['rmsDbAbove18k'] >= -80:
            integrity_score += map_to_score(row['rmsDbAbove18k'], -80, -70, 15, 25)
        elif row['rmsDbAbove18k'] >= -85:
            integrity_score += map_to_score(row['rmsDbAbove18k'], -85, -80, 5, 15)
        else:
            integrity_score += 0  # 伪造惩罚

    # 动态余量评分 (0-15分)
    if peak_field and pd.notna(row[peak_field]):
        if peak_field == 'peakAmplitudeDb':
            # dB值处理
            if row[peak_field] <= -6.0:
                integrity_score += 15
            elif row[peak_field] <= -3.0:
                integrity_score += map_to_score(row[peak_field], -6.0, -3.0, 15, 10)
            elif row[peak_field] <= -0.1:
                integrity_score += map_to_score(row[peak_field], -3.0, -0.1, 10, 3)
            else:
                integrity_score += 0  # 削波惩罚
        else:
            # 线性值处理
            if row[peak_field] <= 0.5:
                integrity_score += 15
            elif row[peak_field] <= 0.8:
                integrity_score += map_to_score(row[peak_field], 0.5, 0.8, 15, 10)
            elif row[peak_field] <= 0.999:
                integrity_score += map_to_score(row[peak_field], 0.8, 0.999, 10, 3)
            else:
                integrity_score += 0

    # 2. 动态质量评分 (0-30分)
    dynamics_score = 0
    if 'lra' in row and pd.notna(row['lra']) and row['lra'] > 0:
        if 8 <= row['lra'] <= 12:  # 理想区间
            dynamics_score = 30
        elif 6 <= row['lra'] < 8:  # 可接受低动态
            dynamics_score = map_to_score(row['lra'], 6, 8, 20, 28)
        elif 12 < row['lra'] <= 15:  # 高动态
            dynamics_score = map_to_score(row['lra'], 12, 15, 28, 22)
        elif 3 <= row['lra'] < 6:  # 低动态
            dynamics_score = map_to_score(row['lra'], 3, 6, 10, 20)
        elif row['lra'] < 3:  # 极低动态
            dynamics_score = map_to_score(row['lra'], 0, 3, 0, 10)
        else:  # 过高动态
            dynamics_score = 18
    else:
        dynamics_score = 0  # 缺失数据

    # 3. 频谱丰富度评分 (0-30分)
    spectrum_score = 0
    if 'rmsDbAbove16k' in row and pd.notna(row['rmsDbAbove16k']):
        spectrum_score = map_to_score(row['rmsDbAbove16k'], -90, -55, 0, 30)

    total_score = integrity_score + dynamics_score + spectrum_score - completeness_penalty

    # 特殊情况处理
    if '状态' in row:
        if row['状态'] == "可疑 (伪造)":
            total_score = min(total_score, 20)
        elif row['状态'] == "数据不完整":
            total_score = min(total_score, 40)

    return max(0, round(total_score))  # 确保分数不为负


def main():
    """优化后的主执行函数"""
    parser = argparse.ArgumentParser(description="分析由 audio_analyzer (Rust) 生成的 JSON 数据 (v2.1 优化版本)。")
    parser.add_argument("input_json", help="输入的 analysis_data.json 文件路径。")
    parser.add_argument("-o", "--output", default="audio_quality_report_v2.1.csv", help="输出的 CSV 报告文件名。")
    parser.add_argument("--min-score", type=int, default=0, help="只显示高于指定分数的文件 (默认: 0)。")
    parser.add_argument("--show-incomplete", action="store_true", help="显示数据不完整的文件详情。")
    parser.add_argument("--show-stats", action="store_true", help="显示详细统计信息。")

    args = parser.parse_args()

    if not os.path.exists(args.input_json):
        print(f"错误: 输入文件 '{args.input_json}' 不存在。")
        return

    print(f"正在加载数据: {args.input_json}")

    try:
        df = pd.read_json(args.input_json)
    except Exception as e:
        print(f"错误: 无法解析JSON文件: {e}")
        return

    if df.empty:
        print("JSON 文件为空，没有可分析的数据。")
        return

    print(f"数据加载成功！共 {len(df)} 个文件。正在应用优化评分模型...")

    # 数据预处理检查
    field_status = {}
    expected_fields = ['filePath', 'lra', 'rmsDbAbove16k', 'rmsDbAbove18k', 'rmsDbAbove20k']

    # 检查峰值字段
    if 'peakAmplitudeDb' in df.columns:
        expected_fields.append('peakAmplitudeDb')
        field_status['peak_field'] = 'peakAmplitudeDb'
        print("✓ 检测到dB格式峰值数据")
    elif 'peakAmplitude' in df.columns:
        expected_fields.append('peakAmplitude')
        field_status['peak_field'] = 'peakAmplitude'
        print("⚠️ 检测到线性峰值数据，建议Rust端改为输出dB值以提高精度")
    else:
        print("❌ 未检测到峰值数据字段")

    # 检查其他字段
    missing_fields = [field for field in expected_fields if field not in df.columns]
    if missing_fields:
        print(f"⚠️ 缺失字段: {', '.join(missing_fields)}")

    # 应用分析
    print("正在应用启发式规则分析...")
    df[['状态', '备注']] = df.apply(analyze_row, axis=1, result_type='expand')

    print("正在计算质量评分...")
    df['质量分'] = df.apply(calculate_quality_score, axis=1)

    # 数据完整性报告
    if args.show_incomplete:
        incomplete_files = df[df['状态'] == "数据不完整"]
        if not incomplete_files.empty:
            print(f"\n⚠️ 发现 {len(incomplete_files)} 个数据不完整的文件:")
            for _, row in incomplete_files.head(10).iterrows():
                filename = os.path.basename(row['filePath']) if 'filePath' in row else 'Unknown'
                print(f"  - {filename}: {row['备注']}")
            if len(incomplete_files) > 10:
                print(f"  ... 还有 {len(incomplete_files) - 10} 个文件")

    # 生成报告
    output_columns = ['质量分', '状态', 'filePath', '备注', 'lra']

    # 动态添加可用字段
    if field_status.get('peak_field'):
        output_columns.append(field_status['peak_field'])

    additional_fields = ['rmsDbAbove16k', 'rmsDbAbove18k', 'rmsDbAbove20k', 'overallRmsDb']
    for field in additional_fields:
        if field in df.columns:
            output_columns.append(field)

    final_columns = [col for col in output_columns if col in df.columns]

    report_df = df[final_columns].copy()
    report_df = report_df[report_df['质量分'] >= args.min_score]
    report_df.sort_values(by='质量分', ascending=False, inplace=True)

    # 分析摘要
    print(f"\n--- 优化分析摘要 (v2.1) - 共分析 {len(df)} 个文件 ---")

    # 按状态分类统计
    status_counts = df['状态'].value_counts()
    print(f"\n📊 质量状态分布:")
    for status, count in status_counts.items():
        percentage = (count / len(df)) * 100
        print(f"  - {status}: {count} 个文件 ({percentage:.1f}%)")

    # 质量分数分布
    high_quality = len(df[df['质量分'] >= 80])
    medium_quality = len(df[(df['质量分'] >= 60) & (df['质量分'] < 80)])
    low_quality = len(df[df['质量分'] < 60])

    print(f"\n📈 分数分布:")
    print(f"  - 高质量 (80+分): {high_quality} 个文件 ({high_quality / len(df) * 100:.1f}%)")
    print(f"  - 中等质量 (60-79分): {medium_quality} 个文件 ({medium_quality / len(df) * 100:.1f}%)")
    print(f"  - 低质量 (<60分): {low_quality} 个文件 ({low_quality / len(df) * 100:.1f}%)")

    # 可疑文件报告
    suspects = df[df['状态'].str.contains("可疑", na=False)]
    if not suspects.empty:
        print(f"\n⚠️ 检测到 {len(suspects)} 个可疑文件 (疑似伪造/升频):")
        for _, row in suspects.head(5).iterrows():
            filename = os.path.basename(row['filePath']) if 'filePath' in row else 'Unknown'
            print(f"  - [分数: {int(row['质量分'])}] {filename}")
        if len(suspects) > 5:
            print(f"  ... 还有 {len(suspects) - 5} 个可疑文件")
    else:
        print("\n✅ 未检测到明显伪造的文件。")

    # 质量排名
    print(f"\n🏆 质量排名前 5 的文件:")
    for i, (_, row) in enumerate(report_df.head(5).iterrows(), 1):
        filename = os.path.basename(row['filePath']) if 'filePath' in row else 'Unknown'
        print(f"  {i}. [分数: {int(row['质量分'])}] {filename}")

    # 详细统计
    if args.show_stats:
        print(f"\n📊 详细统计信息:")
        if 'lra' in df.columns:
            lra_stats = df['lra'].describe()
            print(
                f"  LRA统计: 平均={lra_stats['mean']:.2f}, 中位数={lra_stats['50%']:.2f}, 标准差={lra_stats['std']:.2f}")

        score_stats = df['质量分'].describe()
        print(
            f"  分数统计: 平均={score_stats['mean']:.1f}, 中位数={score_stats['50%']:.1f}, 标准差={score_stats['std']:.1f}")

    # 输出结果
    try:
        report_df.to_csv(args.output, index=False, encoding='utf-8-sig')
        print(f"\n✅ 完整的分析报告已保存到: {args.output}")
        print(f"包含 {len(report_df)} 个符合条件的文件 (最低分数: {args.min_score})")

        if len(report_df) < len(df):
            filtered_count = len(df) - len(report_df)
            print(f"已过滤掉 {filtered_count} 个低分文件")

    except Exception as e:
        print(f"\n❌ 保存CSV文件时出错: {e}")
        print("尝试将结果输出到控制台...")
        print(report_df.head(10).to_string(index=False))


if __name__ == "__main__":
    main()
